import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {useMutation} from '@tanstack/react-query';
import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {
  KeyboardAwareScrollView,
  KeyboardStickyView,
} from 'react-native-keyboard-controller';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import Check from '@/assets/icons/check.svg';
import LoadingHandler from '@/components/LoadingHandler';
import MButton from '@/components/MButton';
import {useLoan} from '@/hooks/redux';
import {goBack} from '@/navigation/utils/navigation';
import {Footer} from '@/styles/styled-components';
import theme from '@/styles/theme';
import {VerificationCodeInput} from './VerificationCodeInput';

interface QrCodeResponse {
  data: string;
}

interface VerifyTotpResponse {
  success: boolean;
  message?: string;
}

const TwoFactorAuthScreen: React.FC = () => {
  const {accessToken} = useLoan();

  console.log('ACCESS TOKEN >>>', accessToken);

  const [code, setCode] = useState('');
  const [is2FAAlreadyEnabled, setIs2FAAlreadyEnabled] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const height = useBottomTabBarHeight();

  const onSubmit = () => {
    if (code.length === 6) {
      verifyTotpCode(code);
    }
  };

  const fetchQrCode = async (): Promise<QrCodeResponse> => {
    try {
      const response = await fetch('http://192.168.10.157/auth/user/otp/generate', {
        method: 'POST',
        headers: {
          accept: 'image/png',
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      // Check if response is an image
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('image/')) {
        const responseText = await response.text();
        throw new Error(
          `Expected image response, got: ${contentType}. Response: ${responseText}`,
        );
      }

      // Convert the image response to a blob and create a data URL
      const blob = await response.blob();
      return new Promise<QrCodeResponse>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve({data: reader.result as string});
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('QR Code fetch error:', error);
      throw error;
    }
  };

  const verifyTotp = async (totpCode: string): Promise<VerifyTotpResponse> => {
    try {
      const response = await fetch('http://192.168.10.157/auth/user/otp/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          totp: totpCode,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('TOTP verification error:', error);
      throw error;
    }
  };

  // Use React Query's useMutation for the QR code generation
  const {
    mutate: generateQrCode,
    data: qrCodeData,
    isPending: isLoading,
    isError,
    error,
  } = useMutation<QrCodeResponse>({
    mutationFn: fetchQrCode,
    onSuccess: (data) => {
      console.log('QR Code generated successfully:', data);
      setIsInitialLoad(false);
    },
    onError: (error) => {
      console.error('Failed to generate QR code:', error);
      setIsInitialLoad(false);

      // Check if the error indicates 2FA is already enabled
      if (
        error.message.includes('2FA is already enabled') ||
        error.message.includes('"alreadyEnabled":true')
      ) {
        setIs2FAAlreadyEnabled(true);
      }
    },
  });

  // Use React Query's useMutation for TOTP verification
  const {
    mutate: verifyTotpCode,
    isPending: isVerifying,
    isError: isVerifyError,
    error: verifyError,
    isSuccess: isVerifySuccess,
  } = useMutation<VerifyTotpResponse, Error, string>({
    mutationFn: verifyTotp,
    onSuccess: (data) => {
      console.log('TOTP verification successful:', data);
      // Handle successful verification (e.g., navigate to next screen)
    },
    onError: (error) => {
      console.error('TOTP verification failed:', error);
    },
  });

  // Generate QR code when component mounts and accessToken is available
  useEffect(() => {
    if (accessToken) {
      generateQrCode();
    }
  }, [accessToken, generateQrCode]);

  // Skeleton component for QR code loading
  const QRCodeSkeleton = () => (
    <SkeletonPlaceholder borderRadius={8}>
      <View>
        <View style={styles.qr} />
      </View>
    </SkeletonPlaceholder>
  );

  // Skeleton component for verification input
  const VerificationInputSkeleton = () => (
    <SkeletonPlaceholder borderRadius={8}>
      <View>
        <View style={styles.verificationInputSkeleton} />
      </View>
    </SkeletonPlaceholder>
  );

  // Render fallback screen when 2FA is already enabled
  if (is2FAAlreadyEnabled) {
    return (
      <View style={styles.container}>
        <KeyboardAwareScrollView
          style={styles.scrollView}
          contentContainerStyle={[styles.contentContainer, styles.centeredContent]}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.alreadyEnabledContainer}>
            <View style={styles.checkIconContainer}>
              <Check width={80} height={80} />
            </View>

            <Text style={styles.alreadyEnabledTitle}>
              Two-Factor Authentication is Active
            </Text>

            <Text style={styles.alreadyEnabledDescription}>
              Your account is already protected with two-factor authentication. You can
              manage your 2FA settings or disable it from your security settings.
            </Text>

            <View style={styles.securityFeatures}>
              <View style={styles.featureItem}>
                <Text style={styles.featureText}>✓ Enhanced account security</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureText}>✓ Transaction authorization</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureText}>✓ Login protection</Text>
              </View>
            </View>
          </View>
        </KeyboardAwareScrollView>

        <KeyboardStickyView
          offset={{closed: 0, opened: height + (theme.isSmallDevice ? 24 : 40)}}
        >
          <Footer>
            <MButton
              text="Back to Settings"
              onPress={() => {
                goBack();
              }}
            />
          </Footer>
        </KeyboardStickyView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        keyboardShouldPersistTaps="handled"
        bottomOffset={140}
      >
        {isInitialLoad ? (
          <SkeletonPlaceholder borderRadius={4}>
            <View>
              <View style={styles.descTextSkeleton} />
              <View style={styles.descSubTextSkeleton} />
            </View>
          </SkeletonPlaceholder>
        ) : (
          <>
            <Text style={styles.descText}>Set up two-factor authentication.</Text>
            <Text style={styles.descSubText}>
              To be able to authorize transactions you need to scan this QR Code with your
              Authentication App and enter the verification code below.
            </Text>
          </>
        )}

        <View>
          {isInitialLoad ? (
            <QRCodeSkeleton />
          ) : isLoading ? (
            <View style={styles.qr}>
              <LoadingHandler />
            </View>
          ) : qrCodeData?.data ? (
            <TouchableOpacity>
              <Image style={styles.qr} source={{uri: qrCodeData.data}} />
            </TouchableOpacity>
          ) : isError ? (
            <View style={styles.qr}>
              <Text style={styles.errorText}>
                Failed to load QR code
                {error && `\n${error.message}`}
              </Text>
            </View>
          ) : null}
        </View>

        {isInitialLoad ? (
          <>
            <SkeletonPlaceholder borderRadius={4}>
              <View>
                <View style={styles.verificationLabelSkeleton} />
              </View>
            </SkeletonPlaceholder>
            <VerificationInputSkeleton />
          </>
        ) : (
          <>
            <Text style={styles.verificationLabel}>Verification Code</Text>
            <VerificationCodeInput value={code} onChange={setCode} length={6} />
          </>
        )}

        {isVerifySuccess && (
          <Text style={styles.successText}>
            ✓ Two-factor authentication enabled successfully!
          </Text>
        )}

        {isVerifyError && (
          <Text style={styles.errorText}>
            ✗ Invalid verification code. Please try again.
            {verifyError && `\n${verifyError.message}`}
          </Text>
        )}
      </KeyboardAwareScrollView>

      <KeyboardStickyView
        offset={{closed: 0, opened: height + (theme.isSmallDevice ? 24 : 40)}}
      >
        <Footer>
          <MButton
            text={isVerifying ? 'Verifying...' : 'Next'}
            disabled={code.length !== 6 || isVerifying}
            onPress={onSubmit}
          />
        </Footer>
      </KeyboardStickyView>
    </View>
  );
};

export default TwoFactorAuthScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 32,
    paddingTop: 42,
  },
  contentContainer: {
    flexGrow: 1,
  },
  centeredContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  descText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  descSubText: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 24,
  },
  verificationLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    marginTop: 16,
  },
  qr: {
    width: 200,
    height: 200,
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 80,
  },
  successText: {
    color: '#10B981',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 12,
    fontWeight: '500',
  },
  stickyFooter: {
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  // Fallback screen styles
  alreadyEnabledContainer: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  checkIconContainer: {
    marginBottom: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  alreadyEnabledTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 16,
  },
  alreadyEnabledDescription: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 32,
  },
  securityFeatures: {
    width: '100%',
    maxWidth: 280,
  },
  featureItem: {
    marginBottom: 12,
  },
  featureText: {
    fontSize: 14,
    color: '#10B981',
    fontWeight: '500',
    textAlign: 'center',
  },
  // Skeleton styles
  descTextSkeleton: {
    height: 20,
    width: '80%',
    marginBottom: 4,
  },
  descSubTextSkeleton: {
    height: 16,
    width: '100%',
    marginBottom: 24,
  },
  verificationLabelSkeleton: {
    height: 16,
    width: 120,
    marginBottom: 8,
    marginTop: 16,
  },
  verificationInputSkeleton: {
    height: 50,
    width: '100%',
    marginBottom: 16,
  },
});
